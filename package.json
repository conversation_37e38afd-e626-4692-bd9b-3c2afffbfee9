{"name": "influencerflow-automate-campaigns", "version": "1.0.0", "private": true, "dependencies": {"@convostack/langchain-memory": "^0.0.56", "@langchain/core": "^0.3.57", "@langchain/openai": "^0.5.11", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.39.3", "@tanstack/react-query": "^5.18.1", "@types/uuid": "^10.0.0", "axios": "^1.6.7", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "framer-motion": "^11.0.3", "langchain": "^0.3.27", "lucide-react": "^0.323.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.0", "recharts": "^2.11.0", "sonner": "^1.4.0", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "uuid": "^11.1.0", "zod": "^3.22.4", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^20.11.16", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "vite": "^5.0.12"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}}